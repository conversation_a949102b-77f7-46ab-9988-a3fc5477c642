'use client';

import { Button } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';

import { TonLogo } from '@/components/TonLogo';
import { roundToThreeDecimals } from '@/lib/utils';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';

import { sellButtonComponentMessages } from './sell-button-component/intl/sell-button-component.messages';

interface SellButtonComponentProps {
  order: OrderEntity;
  label?: string;
  className?: string;
  tonLogoClassName?: string;
}

export function SellButtonComponent({
  order,
  label,
  className = '',
  tonLogoClassName = '',
}: SellButtonComponentProps) {
  const { formatMessage: t } = useIntl();
  const defaultLabel = label || t(sellButtonComponentMessages.buy);
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const currentPrice = hasSecondaryPrice
    ? order.secondaryMarketPrice
    : order.price;

  return (
    <Button
      className={`w-full [&>h6]:flex [&>h6]:items-center [&>h6]:justify-center [&>h6]:gap-1 ${className}`}
    >
      <div className="flex items-center gap-1">
        <span>{defaultLabel}</span>
        <span className="text-lg font-bold">
          {roundToThreeDecimals(currentPrice)}
        </span>
        <TonLogo size={24} className={`-ml-1 ${tonLogoClassName}`} />
      </div>
    </Button>
  );
}
