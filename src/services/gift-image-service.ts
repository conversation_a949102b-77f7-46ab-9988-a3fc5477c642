export type OrderGift = {
  base_name: string;
  owned_gift_id: string;
  backdrop: {
    name: string;
    colors: {
      center_color: number;
      edge_color: number;
      symbol_color: number;
      text_color: number;
    };
    rarity_per_mille: number;
  };
  model: {
    name: string;
    rarity_per_mille: number;
  };
  symbol: {
    name: string;
    rarity_per_mille: number;
  };
};

export enum AssetFallbackState {
  TGS = 'tgs',
  PNG = 'png',
  FAILED = 'failed',
}

export interface PatternPosition {
  top: number;
  left: number;
  size: {
    width: number;
    height: number;
  };
  opacity: number;
}

/**
 * Generate asset URL for gift models and patterns
 */
export const getGiftAssetUrl = (
  type: 'models' | 'patterns',
  name: string,
  format: AssetFallbackState,
  baseName: string,
): string => {
  return `https://cdn.changes.tg/gifts/${type}/${baseName}/${format === 'png' ? 'png/' : ''}${name}.${format}`;
};

/**
 * Generate pattern array for gift display
 */
export const generateGiftPatternArray = (): PatternPosition[] => {
  const patterns: PatternPosition[] = [];

  // Custom pattern with specific coordinates, opacity, and sizes per row
  const patternData = [
    // Row 1
    { x: 27, y: 6.5, opacity: 0.075, size: 40 },
    { x: 73, y: 6.5, opacity: 0.075, size: 40 },

    // Row 2
    { x: 50, y: 11.5, opacity: 0.125, size: 40 },

    // Row 3
    { x: 36, y: 20.5, opacity: 0.185, size: 55 },
    { x: 64, y: 20.5, opacity: 0.185, size: 55 },

    // Row 4
    { x: 10, y: 26.5, opacity: 0.125, size: 40 },
    { x: 90, y: 26.5, opacity: 0.125, size: 40 },

    // Row 5
    { x: 24, y: 34.5, opacity: 0.185, size: 45 },
    { x: 76, y: 34.5, opacity: 0.185, size: 45 },

    // Row 6
    { x: 0, y: 42.5, opacity: 0.05, size: 40 },
    { x: 100, y: 42.5, opacity: 0.05, size: 40 },

    // Row 7
    { x: 18, y: 50.5, opacity: 0.2, size: 60 },
    { x: 82, y: 50.5, opacity: 0.2, size: 60 },

    // Row 8
    { x: 8, y: 65.5, opacity: 0.125, size: 40 },
    { x: 92, y: 65.5, opacity: 0.125, size: 40 },

    // Row 9
    { x: 25, y: 71.5, opacity: 0.185, size: 50 },
    { x: 75, y: 71.5, opacity: 0.185, size: 50 },

    // Row 10
    { x: 50, y: 74.5, opacity: 0.185, size: 50 },

    // Row 11
    { x: 15, y: 85.5, opacity: 0.125, size: 40 },
    { x: 35, y: 85.5, opacity: 0.125, size: 40 },
    { x: 65, y: 85.5, opacity: 0.125, size: 40 },
    { x: 85, y: 85.5, opacity: 0.125, size: 40 },

    // Row 12
    { x: 50, y: 93.5, opacity: 0.075, size: 40 },
  ];

  patternData.forEach((item) => {
    // Convert percentage to pixels: 100% = 20px, 75% = 15px, etc.
    const pixelSize = (item.size / 100) * 20;

    patterns.push({
      top: item.y,
      left: item.x,
      size: {
        width: pixelSize,
        height: pixelSize,
      },
      opacity: item.opacity,
    });
  });

  return patterns;
};

/**
 * Generate color filter for gift symbols
 */
export const generateGiftColorFilter = (gift: OrderGift): string => {
  if (!gift?.backdrop?.colors?.symbol_color) return '';

  const color = gift.backdrop.colors.symbol_color;
  const r = (color >> 16) & 255;
  const g = (color >> 8) & 255;
  const b = color & 255;

  // Convert to 0-1 range for CSS filter
  const rNorm = r / 255;
  const gNorm = g / 255;
  const bNorm = b / 255;

  // Create a sepia filter followed by hue rotation and saturation
  return `sepia(1) saturate(2) hue-rotate(${(Math.atan2(gNorm - rNorm, bNorm - rNorm) * 180) / Math.PI}deg)`;
};

/**
 * Convert color number to RGB string
 */
const convertColorToRgb = (color: number): string => {
  const r = (color >> 16) & 255;
  const g = (color >> 8) & 255;
  const b = color & 255;
  return `rgb(${r}, ${g}, ${b})`;
};

/**
 * Generate gradient background for gift display
 */
export const generateGiftBackground = (
  gift: OrderGift,
): { background: string } => {
  if (!gift?.backdrop?.colors) {
    return { background: 'radial-gradient(circle, #4a5568 0%, #2d3748 100%)' };
  }

  const centerColor = convertColorToRgb(gift.backdrop.colors.center_color);
  const edgeColor = convertColorToRgb(gift.backdrop.colors.edge_color);

  return {
    background: `radial-gradient(circle, ${centerColor} 0%, ${edgeColor} 100%)`,
  };
};

/**
 * Simple image preloading for performance optimization
 */
export const preloadGiftImage = (src: string): Promise<void> => {
  return new Promise<void>((resolve, reject) => {
    const img = new window.Image();
    img.onload = () => resolve();
    img.onerror = () => reject();
    img.src = src;
  });
};

/**
 * Preload gift assets for faster loading
 */
export const preloadGiftAssets = (
  gift: OrderGift,
  isImage: boolean = false,
): void => {
  if (!gift) return;

  const format = isImage ? AssetFallbackState.PNG : AssetFallbackState.PNG; // Always preload PNG for faster loading
  const modelUrl = getGiftAssetUrl(
    'models',
    gift.model.name,
    format,
    gift.base_name,
  );
  const patternUrl = getGiftAssetUrl(
    'patterns',
    gift.symbol.name,
    format,
    gift.base_name,
  );

  // Preload both assets
  preloadGiftImage(modelUrl).catch(() => {
    // Silently fail - will fallback to normal loading
  });
  preloadGiftImage(patternUrl).catch(() => {
    // Silently fail - will fallback to normal loading
  });
};

/**
 * Check if gift has valid data for rendering
 */
export const isValidGift = (
  gift: OrderGift | null | undefined,
): gift is OrderGift => {
  return !!(gift?.model?.name && gift?.symbol?.name && gift?.base_name);
};

/**
 * Get next fallback state for failed assets
 */
export const getNextFallbackState = (
  currentState: AssetFallbackState,
): AssetFallbackState => {
  switch (currentState) {
    case AssetFallbackState.TGS:
      return AssetFallbackState.PNG;
    case AssetFallbackState.PNG:
      return AssetFallbackState.FAILED;
    default:
      return AssetFallbackState.FAILED;
  }
};
