import bot from "./bot";
import { HealthcheckService } from "./services/healthcheck";
import { expressHttpServer } from "./services/express-server";
import { loadEnvironment } from "./config/env-loader";
import { NODE_ENV, PORT, WEBHOOK_URL, WEB_APP_URL } from "./app.constants";
import {
  logStartupConfiguration,
  logBotLaunchFailed,
  logBotConflictWarning,
  logMenuButtonSetFailed,
  logCommandsSetFailed,
  logBotDescriptionSetFailed,
  logBotStartFailed,
  logGracefulShutdownStarted,
  logGracefulShutdownTimeout,
  logHttpServerStopping,
  logWebhookCleanupSkipped,
  logGracefulShutdownCompleted,
  logShutdownError,
  logBotStarting,
  logMenuButtonConfigured,
  logMenuButtonVerification,
  logCommandsConfigured,
  logBotDescriptionConfigured,
  logBotSetupCompleted,
  logBotStopping,
} from "./index.logger";

loadEnvironment();

logStartupConfiguration({
  NODE_ENV,
  // @ts-expect-error note
  PORT,
  WEBHOOK_URL,
  processId: process.pid,
});

async function startBot() {
  try {
    logBotStarting();

    HealthcheckService.setBotStartTime();

    await expressHttpServer.start();

    bot
      // @ts-expect-error note
      .launch(
        WEBHOOK_URL
          ? {
              webhook: {
                domain: WEBHOOK_URL,
                path: "/webhook",
              },
            }
          : undefined
      )
      .then(() => {
        expressHttpServer.setReady(true);
      })
      .catch((error: any) => {
        logBotLaunchFailed({
          error,
        });
        if (
          error.message?.includes("409") ||
          error.message?.includes("Conflict")
        ) {
          logBotConflictWarning();
        }
      });

    try {
      await bot.telegram.setChatMenuButton({
        menuButton: {
          type: "web_app",
          text: "PREM",
          web_app: {
            url: WEB_APP_URL,
          },
        },
      });

      logMenuButtonConfigured();

      const menuButton = await bot.telegram.getChatMenuButton();
      logMenuButtonVerification({
        menuButtonType: menuButton.type,
        menuButtonText: (menuButton as any).text,
        webAppUrl: (menuButton as any).web_app?.url?.substring(0, 50) + "...",
      });
    } catch (error) {
      logMenuButtonSetFailed({
        error,
        webAppUrl: WEB_APP_URL,
      });
    }

    try {
      await bot.telegram.setMyCommands([
        { command: "start", description: "Start the bot and show main menu" },
        { command: "help", description: "Show help information" },
        { command: "health", description: "Check bot health status" },
      ]);
      logCommandsConfigured();
    } catch (error) {
      logCommandsSetFailed({
        error,
      });
    }

    try {
      const description =
        "Welcome to PREM🎁 – the first liquid pre-market for Telegram unupgraded gifts!\nWith PREM, you can:\n\nAs buyer:\n\n🔓 Buy any unupgraded TG gift.\n💸 Resell for instant profit\n\nAs seller:\n\n🎁 Sell unupgraded TG gift with just 50% collateral.\n💰 Earn fees from resales\n\nEnjoy fast, safe, and easy gift trading!";
      const shortDescription = "🎁 PREM - Telegram Gifts Marketplace";

      await bot.telegram.setMyDescription(description);
      await bot.telegram.setMyShortDescription(shortDescription);

      logBotDescriptionConfigured({
        description,
        shortDescription,
      });
    } catch (error) {
      logBotDescriptionSetFailed({
        error,
      });
    }

    logBotSetupCompleted();
  } catch (error) {
    logBotStartFailed({
      error,
    });
    process.exit(1);
  }
}

async function gracefulShutdown(signal: string) {
  logGracefulShutdownStarted({
    signal,
  });

  const forceExitTimeout = setTimeout(() => {
    logGracefulShutdownTimeout({
      signal,
    });
    process.exit(1);
  }, 8000); // 8 seconds, leaving 2 seconds buffer before Cloud Run SIGKILL

  try {
    logBotStopping();
    bot.stop(signal);

    logHttpServerStopping();
    await expressHttpServer.stop();

    logWebhookCleanupSkipped();

    clearTimeout(forceExitTimeout);
    logGracefulShutdownCompleted();
    process.exit(0);
  } catch (error) {
    clearTimeout(forceExitTimeout);
    logShutdownError({
      error,
    });
    process.exit(1);
  }
}

process.once("SIGINT", () => gracefulShutdown("SIGINT"));
process.once("SIGTERM", () => gracefulShutdown("SIGTERM"));

startBot();
