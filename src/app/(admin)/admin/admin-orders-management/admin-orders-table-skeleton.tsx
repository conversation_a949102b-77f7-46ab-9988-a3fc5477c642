'use client';

import { TableSkeleton } from '@/components/ui/table-skeleton';

interface AdminOrdersTableSkeletonProps {
  rows?: number;
}

export function AdminOrdersTableSkeleton({ rows = 25 }: AdminOrdersTableSkeletonProps) {
  const headerColumns = [
    '', // Checkbox
    'Image',
    'Order Number',
    'Status',
    'Admin Type',
    'Price',
    'Secondary Price',
    'Collection',
    'Buyer',
    'Seller',
    'Created',
    'Actions',
  ];

  return (
    <TableSkeleton
      rows={rows}
      headerColumns={headerColumns}
      showHeader={true}
    />
  );
}
