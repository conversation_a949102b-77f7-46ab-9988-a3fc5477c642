'use client';

import {
  Bar<PERSON>hart3,
  FolderOpen,
  Settings,
  ShoppingCart,
  UserPlus,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import { cn } from '@/lib/utils';

interface MenuItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const menuItems: MenuItem[] = [
  {
    href: '/admin',
    label: 'Dashboard',
    icon: BarChart3,
    description: 'Revenue & Statistics',
  },
  {
    href: '/admin/app-config',
    label: 'App Config',
    icon: Settings,
    description: 'Fees & Settings',
  },
  {
    href: '/admin/collections',
    label: 'Collections',
    icon: FolderOpen,
    description: 'Manage Collections',
  },
  {
    href: '/admin/orders',
    label: 'Orders',
    icon: ShoppingCart,
    description: 'Order Management',
  },
  {
    href: '/admin/users',
    label: 'Users',
    icon: Users,
    description: 'User Management',
  },
  {
    href: '/admin/referrals',
    label: 'Referrals',
    icon: UserPlus,
    description: 'Custom Referrals',
  },
];

export const AdminSidebar = () => {
  const pathname = usePathname();

  return (
    <div className="w-64 bg-white border-r border-gray-200 shadow-sm">
      <div className="p-6">
        <h1 className="text-xl font-bold text-gray-900">Admin Panel</h1>
        <p className="text-sm text-gray-500 mt-1">Marketplace Management</p>
      </div>

      <nav className="px-4 pb-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                    isActive
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900',
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <div className="flex-1">
                    <div>{item.label}</div>
                    <div className="text-xs text-gray-500 mt-0.5">
                      {item.description}
                    </div>
                  </div>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
};
