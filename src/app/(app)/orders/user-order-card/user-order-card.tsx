'use client';

import { Gift } from 'lucide-react';
import { useIntl } from 'react-intl';

import { OrderImage } from '@/components/shared/order-image';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { OrderDeadlineTimer } from '@/components/ui/order/order-deadline-timer';
import { OrderFreezeWarning } from '@/components/ui/order/order-freeze-warning';
import { useOrderTimers } from '@/hooks/use-order-timers';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import { UserType } from '@/mikerudenko/marketplace-shared';
import { OrderStatus } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';
import { isSecondaryMarketOrder } from '@/services/order-service';

import { userOrderCardMessages } from './intl/user-order-card.messages';
import { UserOrderCardHeader } from './user-order-card-header';
import { UserOrderCardInfo } from './user-order-card-info';

interface UserOrderCardProps {
  order: OrderEntity;
  userType: UserType;
  onClick: () => void;
  onSendGift?: () => void;
  onResellOrder?: () => void;
  onActivateOrder?: () => void;
  onGetCancelledGift?: () => void;
}

export function UserOrderCard({
  order,
  userType,
  onClick,
  onSendGift,
  onResellOrder,
  onActivateOrder,
  onGetCancelledGift,
}: UserOrderCardProps) {
  const { formatMessage: t } = useIntl();
  const { collections } = useRootContext();
  const collection =
    collections.find((c) => c.id === order.collectionId) || null;
  const { isFreezed } = useOrderTimers({ order, collection });

  const showDeadlineTimer =
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER;

  const showFreezeWarning =
    order.status === OrderStatus.PAID && isFreezed && userType === 'seller';

  const isSecondary = isSecondaryMarketOrder(order);
  // Show send gift button when:
  // - Order has deadline (meaning it's active)
  // - For sellers: status is PAID
  // - For buyers: status is GIFT_SENT_TO_RELAYER
  const showSendGiftButton =
    order.deadline &&
    onSendGift &&
    ((userType === UserType.SELLER && order.status === OrderStatus.PAID) ||
      (userType === UserType.BUYER &&
        order.status === OrderStatus.GIFT_SENT_TO_RELAYER));

  const showGetGiftButton =
    onSendGift &&
    userType === UserType.BUYER &&
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER;

  // Show resell button when:
  // - User is the buyer
  // - Order status is GIFT_SENT_TO_RELAYER
  // - onResellOrder callback is provided
  const showResellButton =
    userType === UserType.BUYER &&
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER &&
    onResellOrder;

  // Show activate order button when:
  // - User is the seller
  // - Order status is CREATED
  // - onActivateOrder callback is provided
  const showActivateOrderButton =
    userType === UserType.SELLER &&
    order.status === OrderStatus.CREATED &&
    onActivateOrder;

  // Show get cancelled gift button when:
  // - User is the seller
  // - Order status is CANCELLED
  // - Order has a gift field
  // - onGetCancelledGift callback is provided
  const showGetCancelledGiftButton =
    userType === UserType.SELLER &&
    order.status === OrderStatus.CANCELLED &&
    order.gift &&
    onGetCancelledGift;

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't trigger card click if clicking on action buttons
    if (
      (e.target as HTMLElement).closest('[data-send-gift-button]') ||
      (e.target as HTMLElement).closest('[data-resell-button]') ||
      (e.target as HTMLElement).closest('[data-activate-order-button]') ||
      (e.target as HTMLElement).closest('[data-get-cancelled-gift-button]')
    ) {
      return;
    }
    onClick();
  };

  const handleSendGiftClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onSendGift?.();
  };

  const handleResellClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onResellOrder?.();
  };

  const handleActivateOrderClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onActivateOrder?.();
  };

  const handleGetCancelledGiftClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onGetCancelledGift?.();
  };

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={handleCardClick}
    >
      <CardContent className="p-2 flex flex-col h-full">
        <div className="relative mb-1">
          <OrderImage
            order={order}
            collection={collection}
            className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]"
          >
            <UserOrderCardHeader order={order} />
            {isSecondary && (
              <SecondaryMarketBadge className="absolute top-1.5 right-1.5" />
            )}
          </OrderImage>
        </div>

        <UserOrderCardInfo order={order} collection={collection} />

        {showDeadlineTimer && (
          <OrderDeadlineTimer
            {...{
              order,
              collection,
              userType,
            }}
            className={showFreezeWarning ? 'mb-1' : ''}
          />
        )}

        {showFreezeWarning && (
          <OrderFreezeWarning
            order={order}
            userType={userType}
            collection={collection}
            isFreezed={isFreezed}
          />
        )}

        {showSendGiftButton && (
          <Button
            data-send-gift-button
            onClick={handleSendGiftClick}
            className="mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-sm py-2"
          >
            <Gift className="w-4 h-4 mr-2" />
            {t(userOrderCardMessages.sendAGift)}
          </Button>
        )}

        {showGetGiftButton && (
          <Button
            data-get-gift-button
            onClick={handleSendGiftClick}
            className="mt-2 w-full bg-purple-500 hover:bg-purple-600 text-white text-sm py-2"
          >
            <Gift className="w-4 h-4 mr-2" />
            {t(userOrderCardMessages.getAGift)}
          </Button>
        )}

        {showResellButton && (
          <Button
            data-resell-button
            onClick={handleResellClick}
            className="mt-2 w-full bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white text-sm py-2"
          >
            <Gift className="w-4 h-4 mr-2" />
            {t(userOrderCardMessages.resellThisOrder)}
          </Button>
        )}

        {showActivateOrderButton && (
          <Button
            data-activate-order-button
            onClick={handleActivateOrderClick}
            className="mt-2 w-full bg-orange-500 hover:bg-orange-600 text-white text-sm py-2"
          >
            <Gift className="w-4 h-4 mr-2" />
            {t(userOrderCardMessages.activateOrder)}
          </Button>
        )}

        {showGetCancelledGiftButton && (
          <Button
            data-get-cancelled-gift-button
            onClick={handleGetCancelledGiftClick}
            className="mt-2 w-full bg-red-500 hover:bg-red-600 text-white text-sm py-2"
          >
            <Gift className="w-4 h-4 mr-2" />
            {t(userOrderCardMessages.getCancelledGift)}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
