'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  clearOrderDeadlines,
  recalculateOrderDeadlines,
} from '@/api/admin-api';
import { createCollection, updateCollection } from '@/api/collection-api';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-picker';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import type { CollectionEntity } from '@/mikerudenko/marketplace-shared';
import {
  CollectionStatus,
  firebaseTimestampToDate,
} from '@/mikerudenko/marketplace-shared';
import { useCollectionStatusText } from '@/services/collection-status-service';

const collectionSchema = z.object({
  id: z.string().min(1, 'Collection ID is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  status: z.nativeEnum(CollectionStatus),
  floorPrice: z.number().min(0, 'Floor price must be non-negative'),
  launchedAt: z.date().nullable().optional(),
  active: z.boolean(),
  lock_period: z.string().optional(),
});

type CollectionFormData = z.infer<typeof collectionSchema>;

interface ManageCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  collection: CollectionEntity | null;
  onSave: () => void;
}

export const ManageCollectionModal = ({
  isOpen,
  onClose,
  collection,
  onSave,
}: ManageCollectionModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const getCollectionStatusText = useCollectionStatusText();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CollectionFormData>({
    resolver: zodResolver(collectionSchema),
    defaultValues: {
      id: '',
      name: '',
      description: '',
      status: CollectionStatus.PREMARKET,
      floorPrice: 0,
      launchedAt: undefined,
      active: true,
    },
  });

  useEffect(() => {
    if (collection) {
      const launchedAtDate = collection.launchedAt
        ? firebaseTimestampToDate(collection.launchedAt)
        : undefined;

      reset({
        id: collection.id,
        name: collection.name,
        description: collection.description,
        status: collection.status,
        floorPrice: collection.floorPrice || 0,
        launchedAt: launchedAtDate ?? null,
        active: collection.active ?? true,
        lock_period: String(collection.lock_period ?? 0),
      });
    } else {
      reset({
        id: '',
        name: '',
        description: '',
        status: CollectionStatus.PREMARKET,
        floorPrice: 0,
        launchedAt: undefined,
        active: true,
        lock_period: undefined,
      });
    }
  }, [collection, reset]);

  const onSubmit = async (data: CollectionFormData) => {
    setIsSubmitting(true);

    if (Number.isNaN(data.lock_period)) {
      alert('Lock period must be a number');
      setIsSubmitting(false);
      return;
    }

    try {
      // Handle launchedAt field logic
      const updateData: Partial<CollectionEntity> = {
        id: data.id,
        name: data.name,
        description: data.description,
        status: data.status,
        floorPrice: data.floorPrice,
        active: data.active,
        lock_period: Number(data.lock_period),
      };
      const isUpdatingExistingCollection = !!collection;
      const originalLaunchedAt = collection?.launchedAt;

      // Convert launchedAt string to Date if provided, or explicitly set to undefined if not provided
      if (data.launchedAt) {
        updateData.launchedAt = new Date(data.launchedAt);
      } else {
        // Leave launchedAt undefined when clearing the field
        // @ts-expect-error note
        updateData.launchedAt = null;
      }

      // If changing status from PREMARKET to MARKET and launchedAt is not set
      if (
        !data.launchedAt &&
        data.status === CollectionStatus.MARKET &&
        collection?.status === CollectionStatus.PREMARKET &&
        !collection.launchedAt
      ) {
        updateData.launchedAt = new Date();
      }

      // Save the collection first
      if (collection) {
        await updateCollection(collection.id, updateData);
      } else {
        const newCollectionData: CollectionEntity = {
          id: data.id,
          name: data.name,
          description: data.description,
          status: data.status,
          floorPrice: data.floorPrice,
          active: data.active,
          lock_period: Number(data.lock_period),
          // @ts-expect-error note
          launchedAt: data.launchedAt || null,
        };
        await createCollection(newCollectionData);
      }

      // Handle deadline recalculation for existing collections only
      if (isUpdatingExistingCollection && collection) {
        const newLaunchedAt = updateData.launchedAt;
        const launchedAtChanged =
          (originalLaunchedAt
            ? firebaseTimestampToDate(originalLaunchedAt)?.getTime()
            : null) !==
          (firebaseTimestampToDate(newLaunchedAt)?.getTime?.() || null);

        if (launchedAtChanged) {
          try {
            if (newLaunchedAt) {
              // LaunchedAt was set or changed - recalculate deadlines
              await recalculateOrderDeadlines(collection.id);
              console.log('Order deadlines recalculated successfully');
            } else {
              // LaunchedAt was cleared - clear all deadlines
              await clearOrderDeadlines(collection.id);
              console.log('Order deadlines cleared successfully');
            }
          } catch (deadlineError) {
            console.error('Error updating order deadlines:', deadlineError);
            // Don't fail the entire operation if deadline update fails
          }
        }
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving collection:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {collection ? 'Edit Collection' : 'Add Collection'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="id">Collection ID</Label>
            <Input
              id="id"
              {...register('id')}
              placeholder="Enter collection ID"
            />
            {errors.id && (
              <p className="text-sm text-red-600">{errors.id.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="Enter collection name"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter collection description"
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-600">
                {errors.description.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="floorPrice">Floor Price (TON)</Label>
            <Input
              id="floorPrice"
              type="number"
              step="0.01"
              min="0"
              {...register('floorPrice', { valueAsNumber: true })}
              placeholder="Enter minimum floor price"
            />
            {errors.floorPrice && (
              <p className="text-sm text-red-600">
                {errors.floorPrice.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lock_period">Lock Period (days)</Label>
            <Input
              id="lock_period"
              type="string"
              step="1"
              min="1"
              {...register('lock_period')}
              placeholder="Enter lock period in days (optional)"
            />
            <p className="text-sm text-gray-500">
              Collection-specific lock period. Leave empty to use app default
              (21 days)
            </p>
            {errors.lock_period && (
              <p className="text-sm text-red-600">
                {errors.lock_period.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={watch('status')}
              onValueChange={(value) =>
                setValue('status', value as CollectionStatus)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(CollectionStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {getCollectionStatusText(status)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="active">Active</Label>
              <Switch
                id="active"
                checked={watch('active')}
                onCheckedChange={(checked) => setValue('active', checked)}
              />
            </div>
            <p className="text-sm text-gray-500">
              When inactive, users cannot create orders for this collection
            </p>
            {errors.active && (
              <p className="text-sm text-red-600">{errors.active.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="launchedAt">Launched At</Label>
              {watch('launchedAt') && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setValue('launchedAt', undefined)}
                  className="h-6 px-2 text-xs"
                >
                  Clear
                </Button>
              )}
            </div>
            <DateTimePicker
              // @ts-expect-error note
              date={watch('launchedAt')}
              onDateChange={(date) => setValue('launchedAt', date)}
              placeholder="Select launch date and time"
            />
            <p className="text-sm text-gray-500">
              Clear this field to set launchedAt to null
            </p>
            {errors.launchedAt && (
              <p className="text-sm text-red-600">
                {errors.launchedAt.message}
              </p>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : collection ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
