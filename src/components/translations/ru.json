{"actions.apply": "Применить", "actions.buy": "Купить", "actions.cancel": "Отмена", "actions.close": "Закрыть", "actions.confirm": "Подтвердить", "actions.delete": "Удалить", "actions.edit": "Редактировать", "actions.fulfill": "Исполнить", "actions.pause": "Пауза", "actions.reject": "Отклонить", "actions.resend": "Отправить повторно", "actions.save": "Сохранить", "actions.saved": "Сохранено", "actions.send": "Отправить", "actions.sent": "Отправлено", "actions.signIn": "Войти", "actions.signOut": "Выйти", "actions.signUp": "Зарегистрироваться", "actions.submit": "Отправить", "collection.status.deleted": "Удалено", "collection.status.market": "Рынок", "collection.status.premarket": "Предпродажа", "collectionName.unknownCollection": "Неизвестная коллекция", "collectionSelect.collection": "Коллекция", "collectionSelect.noCollectionsFound": "Коллекции, соответствующие \"{searchQuery}\", не найдены.", "collectionSelect.searchCollections": "Поиск коллекций...", "collectionSelect.selectCollection": "Выберите коллекцию...", "countdownPopup.closeNotification": "Закрыть уведомление", "countdownPopup.depositProcessing": "Обработка депозита", "countdownPopup.minutes": "минут", "countdownPopup.youWillReceiveFundsWithin": "Вы получите средства в течение", "depositDrawer.actions.cancel": "Отмена", "depositDrawer.actions.deposit": "Депозит", "depositDrawer.actions.pleaseConnectWallet": "Пожалуйста, подключите кошелек для внесения депозита", "depositDrawer.actions.processing": "Обработка...", "depositDrawer.addTonToBalance": "Добавить TON на баланс маркетплейса", "depositDrawer.amountInput.amountMustBeAtLeast": "Сумма должна быть не менее {amount} TON", "depositDrawer.amountInput.depositAmountTon": "Сумма депозита (TON)", "depositDrawer.amountInput.minTonPlaceholder": "Мин {amount} TON", "depositDrawer.close": "Закрыть", "depositDrawer.copyTransactionHash": "Скопировать хеш транзакции", "depositDrawer.depositCompleted": "Ваш депозит успешно завершён", "depositDrawer.depositFee": "Комиссия депозита:", "depositDrawer.depositFunds": "Внести средства", "depositDrawer.depositInformation": "Информация о депозите", "depositDrawer.depositProcessing": "Обработка депозита", "depositDrawer.depositSuccess": "Депозит успешен!", "depositDrawer.loadingConfiguration": "Загрузка конфигурации...", "depositDrawer.minimumDeposit": "Минимальный депозит:", "depositDrawer.processingYourDeposit": "Обработка вашего депозита...", "depositDrawer.summary.depositAmount": "Сумма депозита:", "depositDrawer.summary.depositFee": "Комиссия депозита:", "depositDrawer.summary.totalToPay": "Всего к оплате:", "depositDrawer.transactionHashCopied": "Хеш транзакции скопирован в буфер обмена", "depositDrawer.viewOnTonScan": "Посмотреть в TON Scan", "depositDrawer.youWillReceiveFundsWithin": "Вы получите средства в течение", "errorPage.tryAgain": "Попробовать снова", "errorPage.unhandledErrorOccurred": "Произошла необработанная ошибка!", "errors.auth.adminOnly": "Только администраторы могут выполнить эту операцию.", "errors.auth.permissionDenied": "Доступ запрещен.", "errors.auth.permissionDeniedWithOperation": "Вы можете выполнить {operation} только для себя.", "errors.auth.tonWalletRequired": "У пользователя не настроен адрес кошелька TON.", "errors.auth.unauthenticated": "Требуется аутентификация.", "errors.auth.userNotFound": "Пользователь не найден.", "errors.fulfillAndResell.insufficientBalance": "Недостаточно средств для создания заказа на перепродажу.", "errors.fulfillAndResell.invalidOrderStatus": "Заказ должен иметь статус \"подарок отправлен релейеру\" для перепродажи.", "errors.fulfillAndResell.invalidParameters": "ID заказа и цена перепродажи обязательны и должны быть действительными.", "errors.fulfillAndResell.notOrderBuyer": "Вы не являетесь покупателем этого заказа.", "errors.fulfillAndResell.orderNotFound": "Заказ не найден.", "errors.generic.authenticationFailed": "Ошибка аутентификации.", "errors.generic.operationFailed": "Операция не удалась. Попробуйте снова.", "errors.generic.serverError": "Произошла ошибка сервера.", "errors.generic.unknownError": "Произошла неизвестная ошибка.", "errors.order.buyerCannotPurchaseSameOrder": "Вы не можете купить тот же заказ снова.", "errors.order.collectionNotActive": "Коллекция не активна.", "errors.order.collectionNotFound": "Коллекция не найдена.", "errors.order.insufficientBalance": "Недостаточно средств.", "errors.order.onlyBuyerCanSetSecondaryPrice": "Только текущий покупатель может установить цену на вторичном рынке.", "errors.order.onlyPaidOrdersPurchasable": "На вторичном рынке можно покупать только заказы со статусом ОПЛАЧЕН.", "errors.order.onlyPaidOrdersSecondaryMarket": "На вторичном рынке можно размещать только заказы со статусом ОПЛАЧЕН.", "errors.order.orderMustBeGiftSentStatus": "Заказ должен иметь статус 'подарок отправлен релейеру' для завершения покупки.", "errors.order.orderMustBePaidStatus": "Заказ должен иметь статус 'оплачен' для отправки подарка релейеру.", "errors.order.orderMustHaveBuyerAndSeller": "У заказа должны быть и покупатель, и продавец для размещения на вторичном рынке.", "errors.order.orderNotAvailableSecondaryMarket": "Заказ недоступен на вторичном рынке.", "errors.order.orderNotFound": "Заказ не найден.", "errors.order.secondaryPriceBelowMinimum": "Цена на вторичном рынке должна быть не менее {minPrice} TON.", "errors.order.secondaryPriceExceedsCollateral": "Цена на вторичном рынке не может превышать общий залог {totalCollateral} TON (покупатель: {buyerAmount} TON + продавец: {sellerAmount} TON).", "errors.order.sellerCannotPurchaseOwnOrder": "Продавеац не может купить свой собственный заказ на вторичном рынке.", "errors.telegram.botTokenNotConfigured": "Токен Telegram бота не настроен.", "errors.telegram.firebaseAuthError": "Произошла ошибка Firebase Auth.", "errors.telegram.iamPermissionError": "У сервисного аккаунта Firebase нет необходимых разрешений IAM для создания пользовательских токенов.", "errors.telegram.initDataRequired": "Требуется initData.", "errors.telegram.invalidTelegramData": "Неверные данные Telegram.", "errors.validation.botTokenRequired": "Требуется токен бота.", "errors.validation.invalidBotToken": "Неверный токен бота.", "errors.validation.invalidCollectionId": "Требуется корректный ID коллекции.", "errors.validation.invalidOrderId": "Требуется корректный ID заказа.", "errors.validation.invalidPrice": "Требуется корректная цена.", "errors.validation.invalidSecondaryMarketPrice": "Требуется корректная цена для вторичного рынка.", "errors.validation.ownedGiftIdRequired": "Требуется ID принадлежащего подарка.", "errors.validation.positiveAmountRequired": "{fieldName} должно быть больше 0.", "errors.validation.requiredField": "{field} обязательно для заполнения.", "errors.validation.userIdOrTgIdRequired": "Требуется userId или tgId.", "errors.withdrawal.amountAboveMaximum": "Сумма вывода не может превышать {maxAmount} TON.", "errors.withdrawal.amountBelowMinimum": "Сумма вывода должна быть не менее {minAmount} TON.", "errors.withdrawal.amountExceeds24hLimit": "Сумма вывода превышает лимит за 24 часа. Вы можете вывести до {remainingLimit} TON. Лимит обновится в {resetAt}.", "errors.withdrawal.amountTooSmallAfterFees": "Сумма слишком мала после вычета комиссий.", "errors.withdrawal.calculationFailed": "Не удалось рассчитать статус вывода", "errors.withdrawal.insufficientAvailableBalance": "Недостаточно доступных средств для вывода.", "footer.marketplace": "Маркетплейс", "footer.myOrders": "Мои заказы", "footer.myProfile": "Мой профиль", "fulfillAndResellDrawer.availableBalance": "Доступный баланс:", "fulfillAndResellDrawer.cancel": "Отмена", "fulfillAndResellDrawer.fulfillAndResell": "Исполнить и перепродать", "fulfillAndResellDrawer.insufficientBalance": "Недостаточно средств для блокировки {amount} TON", "fulfillAndResellDrawer.lockAmount": "Сумма блокировки:", "fulfillAndResellDrawer.lockPercentage": "Процент блокировки:", "fulfillAndResellDrawer.processing": "Обработка...", "fulfillAndResellDrawer.resellInformation": "Информация о перепродаже заказа", "fulfillAndResellDrawer.resellPrice": "Цена перепродажи (TON)", "fulfillAndResellDrawer.title": "Исполнить и перепродать заказ", "freezePeriodStatus.expired": "Истёк", "freezePeriodStatus.freezePeriodEnded": "Период заморозки завершён", "freezePeriodStatus.freezePeriodNotStarted": "Период заморозки ещё не начался", "freezePeriodStatus.timeRemaining": "Осталось {days}д {hours}ч {minutes}м {seconds}с", "giftInfoDrawer.claimGiftSteps": "Следуйте этим шагам, чтобы получить подарок от релейера", "giftInfoDrawer.claimYourGift": "Получить подарок", "giftInfoDrawer.sendGiftSteps": "Следуйте этим шагам, чтобы отправить подарок релейеру", "giftInfoDrawer.sendGiftToRelayer": "Отправить подарок релейеру", "header.deposit": "Депозит", "header.profile": "Профиль", "header.walletDisconnected": "Кошелек отключён", "header.withdraw": "Вывод", "insufficientBalance.topUp": "Пополнить баланс", "loginModal.authenticationRequired": "Требуется аутентификация", "loginModal.mustBeLoggedIn": "Вы должны войти в систему для выполнения этого действия.", "loginModal.signInWithTelegram": "Войти через Telegram", "loginModal.signingIn": "Вход...", "marketplace.activity.executedOrdersDescription": "Выполненные заказы будут отображены здесь", "marketplace.activity.noActivityFound": "Активность не найдена", "marketplace.activity.orderNumber": "Заказ #{number}", "marketplace.activity.viewOrder": "Посмотреть заказ", "marketplace.createOrder.cancel": "Отмена", "marketplace.createOrder.collection": "Коллекция", "marketplace.createOrder.create": "Создать", "marketplace.createOrder.createSellOrder": "Создать заказ на продажу", "marketplace.createOrder.creating": "Создание...", "marketplace.createOrder.enterPrice": "Введите цену в TON", "marketplace.createOrder.insufficientBalance": "Недостаточно средств", "marketplace.createOrder.maximumPrice": "Максимальная цена {amount} TON", "marketplace.createOrder.minimumPrice": "Минимальная цена {amount} TON", "marketplace.createOrder.price": "Цена", "marketplace.createOrder.selectCollection": "Выберите коллекцию...", "marketplace.resell.cancel": "Отмена", "marketplace.resell.createResaleOrder": "Создать заказ на перепродажу", "marketplace.resell.importantNotice": "Важное уведомление", "marketplace.resell.importantNoticeDescription": "После установки цены перепродажи ваш заказ будет размещён на вторичном рынке. Другие пользователи смогут купить его по установленной вами цене.", "marketplace.resell.loadingYourOrders": "Загрузка ваших заказов...", "marketplace.resell.minimumPrice": "Минимум: {minPrice} TON", "marketplace.resell.minimumTonPlaceholder": "Минимум {minPrice} TON", "marketplace.resell.noOrdersFoundToResell": "Заказы для перепродажи не найдены", "marketplace.resell.originalPrice": "Первоначальная цена", "marketplace.resell.resalePriceTon": "Цена перепродажи (TON)", "marketplace.resell.resellMyOrder": "Перепродать мой заказ", "marketplace.resell.selectOrderToResell": "Выберите заказ, который вы купили, для перепродажи на вторичном рынке", "marketplace.resell.setResalePrice": "Установить цену перепродажи", "marketplace.resell.setResalePriceButton": "Установить цену перепродажи", "marketplace.resell.setResalePriceSubtitle": "Установите цену для перепродажи этого заказа на вторичном рынке", "marketplace.resell.settingPrice": "Установка цены...", "marketplace.resell.tooHigh": "✗ Слишком высоко", "marketplace.resell.tooLow": "✗ Слишком низко", "marketplace.resell.updateResaleOrder": "Обновить заказ на перепродажу", "marketplace.resell.valid": "✓ Действительно", "marketplace.tabs.activity": "Активность", "marketplace.tabs.buy": "Купить", "marketplace.tabs.sell": "Продать", "marketplaceFilters.allCollections": "Все коллекции", "marketplaceFilters.max": "Макс цена", "marketplaceFilters.min": "<PERSON><PERSON><PERSON> цена", "marketplaceFilters.newestFirst": "Сначала новые", "marketplaceFilters.oldestFirst": "Сначала старые", "marketplaceFilters.priceHighToLow": "Сначала дорогие", "marketplaceFilters.priceLowToHigh": "Сначала дешевые", "marketplaceFilters.sortBy": "Сортировка", "mock.message": "Тестовое сообщение", "notFound.redirecting": "Перенаправление...", "notFound.takingYouBackToMarketplace": "Возвращаем вас на маркетплейс", "nouns.confirmation": "Подтверждение", "nouns.description": "Описание", "nouns.email": "Email", "nouns.error": "Ошибка", "nouns.from": "От", "nouns.name": "Имя", "nouns.orderNumber": "Заказ №{number}", "nouns.password": "Пароль", "nouns.price": "Цена", "nouns.service": "Сервис", "orderActors.anonymousUser": "Анонимный пользователь", "orderActors.buyer": "Покупатель", "orderActors.noBuyerAssigned": "Покупатель не назначен", "orderActors.noRoleAssigned": "{role} не назначен", "orderActors.noSellerAssigned": "Продавец не назначен", "orderActors.resseller": "Перепродавец", "orderActors.seller": "Продавец", "orderDeadlineTimer.deadline": "Крайний срок", "orderDeadlineTimer.giftWillBecomeTransferable": "Подарок скоро станет передаваемым", "orderDeadlineTimer.sellerMustSend": "Продавец должен отправить", "orderDeadlineTimer.sendOrLoseCollateral": "Отправить или потерять залог", "orderDeadlineTimer.waiting": "Ожидание", "orderDetails.content.action": "Действие", "orderDetails.content.buy": "Купить", "orderDetails.content.fulfill": "Выполнить", "orderDetails.content.insufficientBalance": "Недостаточно средств для выполнения этого действия", "orderDetails.content.openingTelegram": "Открытие Telegram...", "orderDetails.content.share": "Поделиться", "orderDetails.content.showResellHistory": "Показать историю перепродаж", "orderDetails.fees.buyer": "Покупатель", "orderDetails.fees.collateral": "Залог", "orderDetails.fees.collateralDescription": "{buyerPercentage}% залог для покупателей. Заблокирован до выполнения заказа. Мгновенно возвращается, если заказ не выполнен.", "orderDetails.fees.deposited": "Внесено", "orderDetails.fees.feePaidBySeller": "Комиссия {feePercent}%. Оплачивается продавцом.", "orderDetails.fees.orderDetailsAndFees": "Детали заказа и комиссии", "orderDetails.fees.purchaseFee": "Комиссия за покупку", "orderDetails.fees.seller": "Продавец", "orderDetails.lastUpdate": "Последнее обновление", "orderPageClient.failedToLoadOrder": "Не удалось загрузить заказ", "orderPageClient.orderNotFound": "Заказ не найден", "orderPageClient.redirectingToHome": "Перенаправление на главную...", "orderStatus.active": "Активный", "orderStatus.cancelled": "Отменён", "orderStatus.created": "Создан", "orderStatus.fulfilled": "Выполнен", "orderStatus.giftSentToRelayer": "Отправлен боту", "orderStatus.paid": "Оплачен", "orderStatusUtils.active": "Активный", "orderStatusUtils.buyerDeadline": "Срок для покупателя", "orderStatusUtils.buyerMustClaimGiftOrLoseCollateral": "Покупатель должен забрать подарок или потеряет залог", "orderStatusUtils.cancelled": "Отменён", "orderStatusUtils.claimGiftFromRelayerOrLoseCollateral": "Заберите подарок у релейера или потеряете залог", "orderStatusUtils.created": "Создан", "orderStatusUtils.deadline": "Крайний срок", "orderStatusUtils.fulfilled": "Выполнен", "orderStatusUtils.giftSentToRelayer": "Отправлен боту", "orderStatusUtils.paid": "Оплачен", "orderStatusUtils.sellerDeadline": "Срок для продавца", "orderStatusUtils.sellerMustSend": "Продавец должен отправить", "orderStatusUtils.sellerMustSendGiftOrLoseCollateral": "Продавец должен отправить подарок или потеряет залог", "orderStatusUtils.sendGiftToRelayerOrLoseCollateral": "Отправьте подарок релейеру или потеряете залог", "orderStatusUtils.sendOrLoseCollateral": "Отправить или потерять залог", "orderStatusUtils.timeToClaimGift": "Время забрать подарок", "orderStatusUtils.timeToSendGift": "Время отправить подарок", "orders.cancelOrder.cancel": "Отменить", "orders.cancelOrder.cancelOrder": "Отменить заказ", "orders.cancelOrder.cancellationWarning": "Это действие нельзя отменить.", "orders.cancelOrder.cancelling": "Отмена...", "orders.cancelOrder.collateralLossWarning": "Вы потеряете {amount} TON залога.", "orders.cancelOrder.confirmCancellation": "Вы уверены, что хотите отменить этот заказ?", "orders.cancelOrder.failedToCancelOrder": "Не удалось отменить заказ: {message}", "orders.cancelOrder.keepOrder": "Оставить заказ", "orders.cancelOrder.orderCancelledSuccessfully": "Заказ успешно отменён", "orders.cancelOrder.resellerEarningsWarning": "Как перепродавец, вы потеряете потенциальную прибыль.", "orders.cancelOrder.unexpectedError": "Произошла неожиданная ошибка", "orders.clickLoginToSeeOrders": "Нажмите кнопку входа через Telegram, чтобы увидеть ваши заказы", "orders.noBuyOrdersFound": "Заказы на покупку не найдены", "orders.noSellOrdersFound": "Заказы на продажу не найдены", "orders.tabs.myBuyOrders": "Покупка ({count})", "orders.tabs.mySellOrders": "Продажа ({count})", "orders.userOrderCard.activateOrder": "Активировать заказ", "orders.userOrderCard.getAGift": "Получить gift", "orders.userOrderCard.getCancelledGift": "Получить отменённый подарок", "orders.userOrderCard.resellThisOrder": "Перепродать этот заказ", "orders.userOrderCard.sendAGift": "Отправить gift", "orders.youAreNotLoggedIn": "Вы не вошли в систему", "profile.form.displayName": "Отображаемое имя", "profile.form.editProfile": "Редактировать профиль", "profile.form.enterYourDisplayName": "Введите ваше отображаемое имя", "profile.form.failedToUpdateProfile": "Не удалось обновить профиль. Попробуйте снова.", "profile.form.nameIsRequired": "Имя обязательно", "profile.form.nameTooLong": "Имя должно быть меньше 50 символов", "profile.form.profileUpdatedSuccessfully": "Профиль успешно обновлён!", "profile.form.updateProfile": "Обновить профиль", "profile.form.updating": "Обновление...", "profile.main": "Главная", "profile.myTransactions": "Мои транзакции", "profile.referralSection.anonymous": "Анонимный", "profile.referralSection.failedToLoadReferrals": "Не удалось загрузить рефералов", "profile.referralSection.failedToShareReferralLink": "Не удалось поделиться реферальной ссылкой", "profile.referralSection.friends": "друзей", "profile.referralSection.joinMeOnMarketplace": "Присоединяйтесь ко мне на этом удивительном маркетплейсе и начните зарабатывать вознаграждения!", "profile.referralSection.joinTheMarketplace": "Присоединиться к маркетплейсу", "profile.referralSection.loadingReferralData": "Загрузка реферальных данных...", "profile.referralSection.name": "Имя", "profile.referralSection.ofTheirPurchaseFees": "от их комиссий за покупку", "profile.referralSection.points": "<PERSON><PERSON><PERSON><PERSON>", "profile.referralSection.potentialEarnings": "Потенциальный доход", "profile.referralSection.referralLinkCopiedToClipboard": "Реферальная ссылка скопирована в буфер обмена!", "profile.referralSection.referralLinkSharedSuccessfully": "Реферальная ссылка успешно отправлена!", "profile.referralSection.referralProgram": "Реферальная программа", "profile.referralSection.referralRateDescription": "Вы зарабатываете {percentage}% от комиссии за покупку, когда ваши рефералы совершают покупки", "profile.referralSection.shareReferralLink": "Поделиться реферальной ссылкой", "profile.referralSection.shareTheLinkGetPoints": "Поделитесь ссылкой - получите очки за подарки!", "profile.referralSection.sharing": "Отправка...", "profile.referralSection.yourReferralRate": "Ваша реферальная ставка", "profile.referralSection.yourReferrals": "Ваши рефералы ({count})", "profile.settings.animatedCollections": "Анимированные коллекции", "profile.settings.animatedCollectionsDescription": "Включить анимированные превью коллекций и эффекты", "profile.settings.settings": "Настройки", "profile.socialLinks.followUs": "Подписывайтесь на нас", "profile.socialLinks.followUsOn": "Подписывайтесь на нас в {platform}", "profile.transactionHistory.emptyState.noTransactionsYet": "Пока нет транзакций", "profile.transactionHistory.emptyState.transactionHistoryDescription": "История ваших транзакций появится здесь, когда вы начнёте торговать на маркетплейсе", "profile.transactionHistory.header.beta": "БЕТА", "profile.transactionHistory.header.refresh": "Обновить", "profile.transactionHistory.header.transactionHistory": "История транзакций", "profile.transactionHistory.loadingState.loadingYourTransactions": "Загрузка ваших транзакций...", "profile.transactionHistory.pagination.loadingMoreTransactions": "Загрузка дополнительных транзакций...", "profile.transactionHistory.pagination.reachedEndOfHistory": "Вы достигли конца истории транзакций", "profile.transactionHistory.table.amount": "Сумма", "profile.transactionHistory.table.date": "Дата", "profile.transactionHistory.table.description": "Описание", "profile.transactionHistory.table.type": "Тип", "profile.transactionHistory.transactionHistory": "История транзакций", "profile.userInfo.anonymousUser": "Анонимный пользователь", "profile.userInfo.availableBalance": "Доступный баланс", "profile.userInfo.lockedBalance": "Заблокированный баланс", "profile.userInfo.myPoints": "Мои очки", "profile.userInfo.profileInformation": "Информация профиля", "profile.userInfo.totalBalance": "О<PERSON><PERSON><PERSON> баланс", "secondaryMarketBadge.resell": "Перепродажа", "sellButtonComponent.buy": "Купить", "sellPriceDetails.marketFeesIncluded": "комиссии маркетплейса включены", "tonConnect.authenticating": "Аутентификация...", "tonConnect.connect": "Подключить", "tonConnect.connecting": "Подключение...", "tonConnect.disconnect": "Отключить", "transaction.type.buyLockCollateral": "Блокировка покупателя", "transaction.type.cancelationFee": "Комиссия отмены", "transaction.type.deposit": "Депозит", "transaction.type.referralFee": "Реферальная комиссия", "transaction.type.refund": "Возврат", "transaction.type.resellFeeEarnings": "Доход от перепродажи", "transaction.type.sellFulfillment": "Исполнение", "transaction.type.sellLockCollateral": "Блокировка продавца", "transaction.type.unlockCollateral": "Разблокировка", "transaction.type.withdraw": "Вывод", "userOrderActionsSection.cancelOrder": "Отменить заказ", "userOrderActionsSection.createResaleOrder": "Создать заказ на перепродажу", "userOrderActionsSection.showResellHistory": "Показать историю перепродаж", "userOrderActionsSection.updateResaleOrder": "Обновить заказ на перепродажу", "userOrderDeadlineSection.giftWillBecomeTransferableSoon": "Подарок скоро станет доступным для передачи", "userOrderDeadlineSection.waiting": "Ожидание", "userOrderStatusAlerts.freezePeriodActive": "Период заморозки активен", "userOrderStatusAlerts.freezePeriodDescription": "Предметы коллекции пока нельзя передавать. Дождитесь окончания периода заморозки.", "userOrderStatusAlerts.giftReady": "Подарок готов!", "userOrderStatusAlerts.giftReadyDescription": "Ваш подарок был отправлен релейеру. Пожалуйста, посетите бота, чтобы забрать свой подарок.", "userOrderStatusAlerts.giftRefundAvailable": "Возврат подарка доступен", "userOrderStatusAlerts.giftRefundDescription": "Перейдите к релейеру, чтобы вернуть свой подарок.", "userOrderStatusAlerts.openBotForRefund": "Открыть бота для возврата", "userOrderStatusAlerts.openBotToClaim": "Открыть бота, чтобы забрать", "userOrderStatusAlerts.readyToSend": "Готов к отправке", "userOrderStatusAlerts.readyToSendDescription": "Теперь вы можете отправить подарок релейеру.", "userOrderStatusAlerts.waitingForTransfer": "Ожидание передачи", "userOrderStatusAlerts.waitingForTransferDescription": "Дождитесь, пока предмет коллекции станет доступным для передачи.", "withdrawDrawer.amountMustBeAtLeast": "Сумма должна быть не менее 1 TON", "withdrawDrawer.availableBalance": "Доступный баланс:", "withdrawDrawer.cancel": "Отмена", "withdrawDrawer.enterAmountToWithdraw": "Введите сумму для вывода", "withdrawDrawer.insufficientAvailableBalance": "Недостаточно доступных средств", "withdrawDrawer.insufficientBalance": "Недостаточно доступных средств", "withdrawDrawer.invalidAmount": "Неверная сумма", "withdrawDrawer.invalidWithdrawalAmount": "Неверная сумма для вывода", "withdrawDrawer.limitResetsAt": "Лимит обновится в:", "withdrawDrawer.loadingConfiguration": "Загрузка конфигурации...", "withdrawDrawer.max": "Максимум", "withdrawDrawer.minTonPlaceholder": "Мин 1 TON", "withdrawDrawer.minimumWithdrawal": "Минимальный вывод:", "withdrawDrawer.minimumWithdrawalAmount": "Минимальная сумма вывода 1 TON", "withdrawDrawer.netAmount": "Чистая сумма:", "withdrawDrawer.noWalletAddressFound": "Адрес кошелька не найден в вашем профиле", "withdrawDrawer.pleaseConnectWallet": "Пожалуйста, подключите кошелек для вывода средств", "withdrawDrawer.pleaseConnectWalletFirst": "Сначала подключите ваш кошелек", "withdrawDrawer.processing": "Обработка...", "withdrawDrawer.remainingLimit": "Оставшийся лимит:", "withdrawDrawer.unexpectedError": "Произошла неожиданная ошибка", "withdrawDrawer.withdraw": "Вывести", "withdrawDrawer.withdrawAmount": "Сумма вывода:", "withdrawDrawer.withdrawAmountTon": "Сумма вывода (TON)", "withdrawDrawer.withdrawFunds": "Вывести средства", "withdrawDrawer.withdrawTonToWallet": "Вывести TON на подключённый кошелек", "withdrawDrawer.withdrawalFailed": "Вывод не удался: {message}", "withdrawDrawer.withdrawalFee": "Комиссия за вывод:", "withdrawDrawer.withdrawalInformation": "Информация о выводе", "withdrawDrawer.withdrawalLimit24h": "Лимит вывода за 24 часа:", "withdrawDrawer.withdrawalSuccessful": "Вывод успешен! Транзакция: {hash}", "withdrawDrawer.youWillReceive": "Вы получите:", "unifiedGiftInfoDrawer.activateOrderSteps": "Выполните эти шаги, чтобы активировать заказ, отправив подарок", "unifiedGiftInfoDrawer.activateYourOrder": "Активировать ваш заказ", "unifiedGiftInfoDrawer.claimGiftSteps": "Выполните эти шаги, чтобы получить подарок от релейера", "unifiedGiftInfoDrawer.claimYourGift": "Получить ваш подарок", "unifiedGiftInfoDrawer.confirmAndGoToRelayer": "Подтвердите действие и перейдите к {relayerLink}, чтобы получить подарок", "unifiedGiftInfoDrawer.confirmAndSendToRelayer": "Подтвердите действие и отправьте этот подарок к {relayerLink}", "unifiedGiftInfoDrawer.getCancelledGift": "Получить отменённый подарок", "unifiedGiftInfoDrawer.getCancelledGiftSteps": "Выполните эти шаги, чтобы получить отменённый подарок", "unifiedGiftInfoDrawer.goToBot": "Перейти к {botLink}", "unifiedGiftInfoDrawer.goToRelayerToRetrieve": "Перейдите к {relayerLink}, чтобы получить подарок", "unifiedGiftInfoDrawer.pressMuyBuyOrders": "Нажмите кнопку \"Мои заказы на покупку\"", "unifiedGiftInfoDrawer.pressMySellOrders": "Нажмите кнопку \"Мои заказы на продажу\"", "unifiedGiftInfoDrawer.pressMySellOrdersCancelled": "Нажмите кнопку \"Мои заказы на продажу\" и выберите из группы \"Отменённые\"", "unifiedGiftInfoDrawer.pressMySellOrdersPaid": "Нажмите кнопку \"Мои заказы на продажу\" и выберите из группы \"Оплаченные заказы\"", "unifiedGiftInfoDrawer.pressMySellOrdersWaitingActivation": "Нажмите кнопку \"Мои заказы на продажу\" и выберите из группы \"Ожидают активации\"", "unifiedGiftInfoDrawer.selectOrderToActivate": "Выберите заказ, который вы хотите активировать", "unifiedGiftInfoDrawer.selectOrderToGet": "Выберите заказ, который вы хотите получить", "unifiedGiftInfoDrawer.selectOrderToSend": "Выберите заказ, который вы хотите отправить", "unifiedGiftInfoDrawer.sendGiftSteps": "Выполните эти шаги, чтобы отправить подарок релейеру", "unifiedGiftInfoDrawer.sendGiftToRelayer": "Отправить подарок релейеру", "unifiedGiftInfoDrawer.sendGiftToRelayerToActivate": "Отправьте подарок к {relayerLink}, чтобы активировать этот заказ"}