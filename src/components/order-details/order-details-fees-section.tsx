'use client';

import { Check, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';

import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import type { OrderEntity } from '@/mikerudenko/marketplace-shared';
import {
  calculateOrderAmounts,
  formatBPSToPercent,
  getOrderCollateral,
} from '@/services/order-service';

import { orderDetailsFeesMessages } from './intl/order-details-fees-section.messages';

interface OrderDetailsFeesSection {
  order: OrderEntity;
}

export function OrderDetailsFeesSection({ order }: OrderDetailsFeesSection) {
  const { formatMessage: t } = useIntl();
  const [isOpen, setIsOpen] = useState(false);

  const amounts = calculateOrderAmounts(order, true, false);
  const feePercent = formatBPSToPercent(order.fees?.purchase_fee || 500);
  const buyerPercentageZeroDecimal = formatBPSToPercent(
    order.fees?.buyer_locked_percentage || 0,
    0,
  );
  const sellerPercentageZeroDecimal = formatBPSToPercent(
    order.fees?.seller_locked_percentage || 0,
    0,
  );

  const collateralAmount = getOrderCollateral(order, amounts);

  return (
    <div className="space-y-4">
      <Collapsible defaultOpen={true} open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className="flex items-center justify-between w-full p-4 bg-[#1e2337]/50 rounded-lg">
          <span className="text-[#f5f5f5] font-medium">
            {t(orderDetailsFeesMessages.orderDetailsAndFees)}
          </span>
          <ChevronDown
            className={cn(
              'h-4 w-4 text-[#708499] transition-transform',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent className="px-4 pb-4 space-y-4 bg-[#1e2337]/50 rounded-lg">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-[#f5f5f5] font-medium">
                {t(orderDetailsFeesMessages.purchaseFee)}
              </span>
              <TonPriceDisplay
                amount={amounts.purchaseFeeAmount}
                size={16}
                className="text-[#6ab2f2] font-semibold"
                showUnit
              />
            </div>
            <p className="text-xs text-[#708499]">
              {t(orderDetailsFeesMessages.feePaidBySeller, { feePercent })}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-[#f5f5f5] font-medium">
                {t(orderDetailsFeesMessages.collateral)}
              </span>
              <TonPriceDisplay
                amount={collateralAmount}
                size={16}
                className="text-[#6ab2f2] font-semibold"
                showUnit
              />
            </div>

            <div>
              <div className="flex items-center gap-1">
                <span className="text-[#708499] text-xs">
                  {t(orderDetailsFeesMessages.buyer)} -{' '}
                  {buyerPercentageZeroDecimal}% (
                  {amounts.buyerLockedAmount.toFixed(1)} TON)
                </span>
                {order?.buyerId && (
                  <div className="text-green-500 flex items-center text-[10px]">
                    <span>{t(orderDetailsFeesMessages.deposited)}</span>
                    <Check className="h-3" />
                  </div>
                )}
              </div>
              <div className="flex items-center gap-1">
                <span className="text-[#708499] text-xs">
                  {t(orderDetailsFeesMessages.seller)} -{' '}
                  {sellerPercentageZeroDecimal}% (
                  {amounts.sellerLockedAmount.toFixed(1)} TON)
                </span>
                {order?.sellerId && (
                  <div className="text-green-500 flex items-center text-[10px]">
                    <span>{t(orderDetailsFeesMessages.deposited)}</span>
                    <Check className="h-3" />
                  </div>
                )}
              </div>
            </div>

            <p className="text-xs text-[#708499] leading-relaxed">
              {t(orderDetailsFeesMessages.collateralDescription, {
                buyerPercentage: buyerPercentageZeroDecimal,
              })}
            </p>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
