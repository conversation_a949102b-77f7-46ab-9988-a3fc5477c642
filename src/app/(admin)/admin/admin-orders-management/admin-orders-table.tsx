import { Edit, Loader2, Trash2, <PERSON>Circle } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import { TgsOrImage } from '@/components/tgs/tgs-or-image';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  SortableTableHeader,
  type SortDirection,
} from '@/components/ui/sortable-table-header';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type {
  CollectionEntity,
  OrderEntity,
  UserEntity,
} from '@/mikerudenko/marketplace-shared';
import { OrderStatus, UserType } from '@/mikerudenko/marketplace-shared';

interface AdminOrdersTableProps {
  orders: OrderEntity[];
  userCache: Record<string, UserEntity>;
  collections: CollectionEntity[];
  onCancelOrder: (orderId: string) => Promise<void>;
  onDeleteOrder: (orderId: string) => Promise<void>;
  onEditOrder: (order: OrderEntity) => void;
  selectedOrderIds: Set<string>;
  onOrderSelect: (orderId: string, selected: boolean) => void;
  onSelectAll: (selected: boolean) => void;
  sortKey?: string;
  sortDirection?: SortDirection;
  onSort: (key: string, direction: SortDirection) => void;
}

function getAdminUserType(
  order: OrderEntity,
  userCache: Record<string, UserEntity>,
): UserType | null {
  const buyerIsAdmin =
    order.buyerId && userCache[order.buyerId]?.role === 'admin';
  const sellerIsAdmin =
    order.sellerId && userCache[order.sellerId]?.role === 'admin';

  if (buyerIsAdmin && sellerIsAdmin) {
    return UserType.BUYER;
  }
  if (buyerIsAdmin) return UserType.BUYER;
  if (sellerIsAdmin) return UserType.SELLER;
  return null;
}

function formatPrice(price: number): string {
  return `${price.toFixed(2)} TON`;
}

function getStatusBadgeClass(status: OrderStatus): string {
  switch (status) {
    case OrderStatus.CREATED:
      return 'bg-gray-100 text-gray-800';
    case OrderStatus.ACTIVE:
      return 'bg-blue-100 text-blue-800';
    case OrderStatus.PAID:
      return 'bg-green-100 text-green-800';
    case OrderStatus.GIFT_SENT_TO_RELAYER:
      return 'bg-purple-100 text-purple-800';
    case OrderStatus.FULFILLED:
      return 'bg-emerald-100 text-emerald-800';
    default:
      return 'bg-red-100 text-red-800';
  }
}

export function AdminOrdersTable({
  orders,
  userCache,
  collections,
  onCancelOrder,
  onDeleteOrder,
  onEditOrder,
  selectedOrderIds,
  onOrderSelect,
  onSelectAll,
  sortKey,
  sortDirection,
  onSort,
}: AdminOrdersTableProps) {
  const [loadingActions, setLoadingActions] = useState<Set<string>>(new Set());

  const isAllSelected =
    orders.length > 0 &&
    orders.every((order) => selectedOrderIds.has(order.id!));

  const handleCancelOrder = async (orderId: string) => {
    setLoadingActions((prev) => new Set(prev).add(`cancel-${orderId}`));
    try {
      await onCancelOrder(orderId);
    } finally {
      setLoadingActions((prev) => {
        const newSet = new Set(prev);
        newSet.delete(`cancel-${orderId}`);
        return newSet;
      });
    }
  };

  const handleDeleteOrder = async (orderId: string) => {
    setLoadingActions((prev) => new Set(prev).add(`delete-${orderId}`));
    try {
      await onDeleteOrder(orderId);
    } finally {
      setLoadingActions((prev) => {
        const newSet = new Set(prev);
        newSet.delete(`delete-${orderId}`);
        return newSet;
      });
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                onCheckedChange={(checked) => onSelectAll(!!checked)}
                aria-label="Select all orders"
              />
            </TableHead>
            <TableHead>Image</TableHead>
            <TableHead>Order Number</TableHead>
            <SortableTableHeader
              sortKey="status"
              currentSortKey={sortKey}
              currentSortDirection={sortDirection}
              onSort={onSort}
            >
              Status
            </SortableTableHeader>
            <TableHead>Admin Type</TableHead>
            <SortableTableHeader
              sortKey="price"
              currentSortKey={sortKey}
              currentSortDirection={sortDirection}
              onSort={onSort}
            >
              Price
            </SortableTableHeader>
            <SortableTableHeader
              sortKey="secondaryPrice"
              currentSortKey={sortKey}
              currentSortDirection={sortDirection}
              onSort={onSort}
            >
              Secondary Price
            </SortableTableHeader>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {orders.map((order) => {
            const adminType = getAdminUserType(order, userCache);
            const collection = collections.find(
              (c) => c.id === order.collectionId,
            );
            return (
              <TableRow key={order.id}>
                <TableCell>
                  <Checkbox
                    checked={selectedOrderIds.has(order.id!)}
                    onCheckedChange={(checked) =>
                      onOrderSelect(order.id!, !!checked)
                    }
                    aria-label={`Select order #${order.number}`}
                  />
                </TableCell>
                <TableCell>
                  <div className="relative w-8 h-8 rounded-sm overflow-hidden bg-slate-700 flex-shrink-0">
                    <TgsOrImage
                      isImage={true}
                      collectionId={order.collectionId}
                      imageProps={{
                        alt: collection?.name || 'Order item',
                        fill: true,
                        className: 'object-cover',
                      }}
                      tgsProps={{
                        style: {
                          height: 'auto',
                          width: 'auto',
                          padding: '4px',
                        },
                      }}
                    />
                  </div>
                </TableCell>
                <TableCell className="font-mono">
                  <Link
                    href={`/orders/${order.id}`}
                    className="text-[#6ab2f2] hover:text-[#5a9fd8] transition-colors cursor-pointer"
                  >
                    #{order.number}
                  </Link>
                </TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      order.status,
                    )}`}
                  >
                    {order.status}
                  </span>
                </TableCell>
                <TableCell>{adminType || 'N/A'}</TableCell>
                <TableCell>{formatPrice(order.price)}</TableCell>
                <TableCell>
                  {order.secondaryMarketPrice
                    ? formatPrice(order.secondaryMarketPrice)
                    : '-'}
                </TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    {order.status === OrderStatus.ACTIVE && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEditOrder(order)}
                      >
                        <Edit className="h-4 w-4" />
                        Edit
                      </Button>
                    )}
                    {order.status === OrderStatus.CANCELLED ? (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={loadingActions.has(`delete-${order.id}`)}
                          >
                            {loadingActions.has(`delete-${order.id}`) ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                            Delete
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Order</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to permanently delete order
                              #{order.number}? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel
                              disabled={loadingActions.has(
                                `delete-${order.id}`,
                              )}
                            >
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteOrder(order.id!)}
                              disabled={loadingActions.has(
                                `delete-${order.id}`,
                              )}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              {loadingActions.has(`delete-${order.id}`) ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Deleting...
                                </>
                              ) : (
                                'Delete'
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    ) : (
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            disabled={loadingActions.has(`cancel-${order.id}`)}
                          >
                            {loadingActions.has(`cancel-${order.id}`) ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <XCircle className="h-4 w-4" />
                            )}
                            Cancel
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Cancel Order</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to cancel order #
                              {order.number}? This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel
                              disabled={loadingActions.has(
                                `cancel-${order.id}`,
                              )}
                            >
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleCancelOrder(order.id!)}
                              disabled={loadingActions.has(
                                `cancel-${order.id}`,
                              )}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              {loadingActions.has(`cancel-${order.id}`) ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  Cancelling...
                                </>
                              ) : (
                                'Cancel Order'
                              )}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}
