import * as admin from "firebase-admin";
import {
  APP_USERS_COLLECTION,
  AppDate,
  COLLECTION_NAME,
  CollectionEntity,
  CollectionStatus,
  ORDERS_COLLECTION_NAME,
  OrderEntity,
  OrderGift,
  OrderStatus,
  UserEntity,
  formatDateToFirebaseTimestamp,
} from "../../mikerudenko/marketplace-shared";
import { verifyBotToken } from "../../services/bot-auth-service/bot-auth-service";
import { transferNetAmountToSeller } from "../../services/purchase-fee-processing-service";
import { safeMultiply } from "../../utils";
import {
  throwBotTokenRequired,
  throwInvalidBotToken,
  throwInvalidOrderId,
  throwUserIdOrTgIdRequired,
  throwOrderNotFound,
  throwInvalidOrderStatus,
} from "./bot-order-function.error-handler";

export function validateBotToken(botToken?: string): void {
  if (!botToken) {
    throwBotTokenRequired();
  }

  if (!verifyBotToken(botToken)) {
    throwInvalidBotToken();
  }
}

export function validateOrderId(orderId?: string): void {
  if (!orderId) {
    throwInvalidOrderId();
  }
}

export async function getOrderById(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    return {
      success: false,
      order: null,
      message: "Order not found.",
    };
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  let buyer_tg_id: string | undefined;
  let seller_tg_id: string | undefined;

  if (order.buyerId) {
    const buyerDoc = await db
      .collection(APP_USERS_COLLECTION)
      .doc(order.buyerId)
      .get();
    if (buyerDoc.exists) {
      const buyerData = buyerDoc.data() as UserEntity;
      buyer_tg_id = buyerData.tg_id;
    }
  }

  if (order.sellerId) {
    const sellerDoc = await db
      .collection(APP_USERS_COLLECTION)
      .doc(order.sellerId)
      .get();
    if (sellerDoc.exists) {
      const sellerData = sellerDoc.data() as UserEntity;
      seller_tg_id = sellerData.tg_id;
    }
  }

  return {
    success: true,
    order: {
      ...order,
      buyer_tg_id,
      seller_tg_id,
    },
    message: "Order retrieved successfully.",
  };
}

export async function getUserOrders(params: {
  userId?: string;
  tgId?: string;
  botToken: string;
}) {
  const { userId, tgId, botToken } = params;

  validateBotToken(botToken);

  const db = admin.firestore();
  let targetUserId = userId;

  if (!targetUserId && tgId) {
    const usersQuery = await db
      .collection(APP_USERS_COLLECTION)
      .where("tg_id", "==", tgId)
      .limit(1)
      .get();

    if (usersQuery.empty) {
      return {
        success: false,
        orders: [],
        sellOrders: [],
        paidOrdersAwaitingGift: [], // paid orders waiting for gift
        createdOrdersNeedingActivation: [], // created orders for MARKET collections
        cancelledOrdersWithGifts: [], // cancelled orders with gifts for refund
        buyOrders: [],
        count: 0,
        sellOrdersCount: 0,
        paidOrdersAwaitingGiftCount: 0,
        createdOrdersNeedingActivationCount: 0,
        cancelledOrdersWithGiftsCount: 0,
        buyOrdersCount: 0,
        userId: "",
        message: "User not found with the provided Telegram ID.",
      };
    }

    targetUserId = usersQuery.docs[0].id;
  }

  if (!targetUserId) {
    throwUserIdOrTgIdRequired();
  }

  const paidOrdersAwaitingGiftQuery = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", targetUserId)
    .where("status", "==", OrderStatus.PAID)
    .get();

  const createdOrdersNeedingActivationQuery = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", targetUserId)
    .where("status", "==", OrderStatus.CREATED)
    .get();

  const cancelledOrdersWithGiftsQuery = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("sellerId", "==", targetUserId)
    .where("status", "==", OrderStatus.CANCELLED)
    .get();

  const buyOrdersQuery = await db
    .collection(ORDERS_COLLECTION_NAME)
    .where("buyerId", "==", targetUserId)
    .where("status", "==", OrderStatus.GIFT_SENT_TO_RELAYER)
    .get();

  const paidOrdersAwaitingGift: OrderEntity[] = [];
  const createdOrdersNeedingActivation: OrderEntity[] = [];
  const cancelledOrdersWithGifts: OrderEntity[] = [];
  const buyOrders: OrderEntity[] = [];

  paidOrdersAwaitingGiftQuery.forEach((doc) => {
    paidOrdersAwaitingGift.push({ id: doc.id, ...doc.data() } as OrderEntity);
  });

  // Filter created orders for MARKET collections only
  const createdOrdersTemp: OrderEntity[] = [];
  createdOrdersNeedingActivationQuery.forEach((doc) => {
    createdOrdersTemp.push({ id: doc.id, ...doc.data() } as OrderEntity);
  });

  // Get collection data to filter for MARKET status
  const collectionIds = [
    ...new Set(createdOrdersTemp.map((order) => order.collectionId)),
  ];
  const marketCollections = new Set<string>();

  if (collectionIds.length > 0) {
    const collectionsQuery = await db
      .collection(COLLECTION_NAME)
      .where(admin.firestore.FieldPath.documentId(), "in", collectionIds)
      .get();

    collectionsQuery.forEach((doc) => {
      const collection = doc.data() as CollectionEntity;
      if (collection.status === CollectionStatus.MARKET) {
        marketCollections.add(doc.id);
      }
    });
  }

  // Filter created orders to only include MARKET collections
  createdOrdersTemp.forEach((order) => {
    if (marketCollections.has(order.collectionId)) {
      createdOrdersNeedingActivation.push(order);
    }
  });

  // Filter cancelled orders for orders with gifts only
  cancelledOrdersWithGiftsQuery.forEach((doc) => {
    const order = { id: doc.id, ...doc.data() } as OrderEntity;
    if (order.gift && !order.buyerId) {
      cancelledOrdersWithGifts.push(order);
    }
  });

  buyOrdersQuery.forEach((doc) => {
    buyOrders.push({ id: doc.id, ...doc.data() } as OrderEntity);
  });

  const sellOrders = [
    ...paidOrdersAwaitingGift,
    ...createdOrdersNeedingActivation,
    ...cancelledOrdersWithGifts,
  ];
  const allOrders = [...sellOrders, ...buyOrders];

  return {
    success: true,
    orders: allOrders,
    sellOrders,
    paidOrdersAwaitingGift, // paid orders waiting for gift
    createdOrdersNeedingActivation, // created orders for MARKET collections
    cancelledOrdersWithGifts, // cancelled orders with gifts for refund
    buyOrders,
    count: allOrders.length,
    sellOrdersCount: sellOrders.length,
    paidOrdersAwaitingGiftCount: paidOrdersAwaitingGift.length,
    createdOrdersNeedingActivationCount: createdOrdersNeedingActivation.length,
    cancelledOrdersWithGiftsCount: cancelledOrdersWithGifts.length,
    buyOrdersCount: buyOrders.length,
    userId: targetUserId,
    message: "Orders retrieved successfully.",
  };
}

export async function completePurchase(params: {
  orderId: string;
  botToken: string;
}) {
  const { orderId, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  if (order.status !== OrderStatus.GIFT_SENT_TO_RELAYER) {
    throwInvalidOrderStatus();
  }

  // Only update order status - no accounting logic
  await orderDoc.ref.update({
    status: OrderStatus.FULFILLED,
    updatedAt: formatDateToFirebaseTimestamp(
      admin.firestore.Timestamp.now() as AppDate
    ),
  });

  return {
    success: true,
    message: "Purchase completed successfully.",
    order: {
      ...order,
      status: OrderStatus.FULFILLED,
    },
  };
}

export async function sendGiftToRelayer(params: {
  orderId: string;
  gift: OrderGift;
  botToken: string;
}) {
  const { orderId, gift, botToken } = params;

  validateOrderId(orderId);
  validateBotToken(botToken);

  const db = admin.firestore();
  const orderDoc = await db
    .collection(ORDERS_COLLECTION_NAME)
    .doc(orderId)
    .get();

  if (!orderDoc.exists) {
    throwOrderNotFound();
  }

  const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

  // Handle two different order statuses
  if (order.status === OrderStatus.PAID) {
    // Logic for paid orders - send gift to relayer with accounting
    const batch = db.batch();

    // Update order status
    batch.update(orderDoc.ref, {
      gift,
      status: OrderStatus.GIFT_SENT_TO_RELAYER,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    // Transfer funds from buyer's locked balance to seller
    if (order.buyerId && order.sellerId) {
      // Calculate net amount using order's fee structure
      const purchaseFeeRate = order.fees?.purchase_fee || 0;
      const netSellerAmount = safeMultiply(
        order.price,
        1 - purchaseFeeRate / 10000
      ); // Convert BPS to decimal

      // Use reusable function for money transfer
      await transferNetAmountToSeller(order, netSellerAmount);
    }

    await batch.commit();

    return {
      success: true,
      message: "Gift sent to relayer successfully.",
      order: {
        ...order,
        gift,
        status: OrderStatus.GIFT_SENT_TO_RELAYER,
      },
    };
  } else if (order.status === OrderStatus.CREATED) {
    // New logic for created orders - just update with gift and activate
    await orderDoc.ref.update({
      gift,
      status: OrderStatus.ACTIVE,
      updatedAt: formatDateToFirebaseTimestamp(
        admin.firestore.Timestamp.now() as AppDate
      ),
    });

    return {
      success: true,
      message: "Gift added and order activated successfully.",
      order: {
        ...order,
        gift,
        status: OrderStatus.ACTIVE,
      },
    };
  } else {
    throwInvalidOrderStatus();
  }
}
