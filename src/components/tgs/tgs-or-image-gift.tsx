'use client';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useCallback, useEffect, useState } from 'react';

import { TgsSkeleton } from '@/components/tgs/tgs-skeleton';
import type { OrderGift } from '@/mikerudenko/marketplace-shared';
import {
  AssetFallbackState,
  generateGiftBackground,
  generateGiftColorFilter,
  generateGiftPatternArray,
  getGiftAssetUrl,
  type PatternPosition,
} from '@/services/gift-image-service';
import {
  areGiftAssetsPreloaded,
  cacheGiftAssets,
  getCachedGiftAssets,
} from '@/utils/gift-cache-utils';

const TgsViewer = dynamic(
  () => import('./tgs-viewer').then((mod) => ({ default: mod.TgsViewer })),
  {
    ssr: false,
    loading: () => (
      <TgsSkeleton
        className="w-full h-full"
        style={{ height: '200px', width: '200px' }}
      />
    ),
  },
);

interface TgsOrImageGiftProps {
  isImage: boolean;
  gift: OrderGift;
  className?: string;
  style?: React.CSSProperties;
}

function useGiftAssets(gift: OrderGift, isImage: boolean) {
  const [modelState, setModelState] = useState<AssetFallbackState>(
    isImage ? AssetFallbackState.PNG : AssetFallbackState.TGS,
  );
  const [patternState, setPatternState] = useState<AssetFallbackState>(
    isImage ? AssetFallbackState.PNG : AssetFallbackState.TGS,
  );
  const [modelSrc, setModelSrc] = useState<string>('');
  const [patternSrc, setPatternSrc] = useState<string>('');
  const [modelLoading, setModelLoading] = useState<boolean>(false);
  const [showSkeleton, setShowSkeleton] = useState<boolean>(false);
  const [patternsLoaded, setPatternsLoaded] = useState<number>(0);
  const [totalPatterns, setTotalPatterns] = useState<number>(0);

  useEffect(() => {
    if (!gift || !gift.model?.name || !gift.symbol?.name || !gift.base_name) {
      setModelLoading(false);
      setPatternsLoaded(0);
      setTotalPatterns(0);
      setModelSrc('');
      setPatternSrc('');
      return;
    }

    // Set total patterns count from the pattern array
    const patternArray = generateGiftPatternArray();
    setTotalPatterns(patternArray.length);
    setPatternsLoaded(0);

    // Check if assets are already cached
    const cachedAssets = getCachedGiftAssets(gift, isImage);
    const assetsPreloaded = areGiftAssetsPreloaded(gift, isImage);

    const initialState = isImage
      ? AssetFallbackState.PNG
      : AssetFallbackState.TGS;
    setModelState(initialState);
    setPatternState(initialState);

    let modelUrl: string;
    let patternUrl: string;

    if (cachedAssets) {
      modelUrl = cachedAssets.modelUrl;
      patternUrl = cachedAssets.patternUrl;
    } else {
      modelUrl = getGiftAssetUrl(
        'models',
        gift.model.name,
        initialState,
        gift.base_name,
      );
      patternUrl = getGiftAssetUrl(
        'patterns',
        gift.symbol.name,
        initialState,
        gift.base_name,
      );
      // Cache the URLs for future use
      cacheGiftAssets(gift, isImage);
    }

    setModelSrc(modelUrl);
    setPatternSrc(patternUrl);

    // Only set loading to true if assets aren't preloaded
    if (modelUrl && !assetsPreloaded) {
      setModelLoading(true);
    }

    // Show skeleton if assets aren't preloaded
    if (modelUrl && patternUrl && !assetsPreloaded) {
      setShowSkeleton(true);

      // Fallback timeout to hide skeleton
      const timer = setTimeout(() => {
        setShowSkeleton(false);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [gift, isImage]);

  // Handle loading states based on asset states
  useEffect(() => {
    // If model state is FAILED or we don't have a model source, stop loading
    if (modelState === AssetFallbackState.FAILED || !modelSrc) {
      setModelLoading(false);
    }
  }, [modelState, modelSrc]);

  const handleModelError = () => {
    if (isImage || !gift?.model?.name || !gift?.base_name) {
      setModelState(AssetFallbackState.FAILED);
      setModelLoading(false);
      return;
    }

    switch (modelState) {
      case AssetFallbackState.TGS:
        setModelState(AssetFallbackState.PNG);
        setModelSrc(
          getGiftAssetUrl(
            'models',
            gift.model.name,
            AssetFallbackState.PNG,
            gift.base_name,
          ),
        );
        break;
      case AssetFallbackState.PNG:
        setModelState(AssetFallbackState.FAILED);
        setModelLoading(false);
        break;
    }
  };

  const handleModelLoad = useCallback(() => {
    setModelLoading(false);
    // Hide skeleton when model is loaded AND all patterns are loaded
    if (patternsLoaded >= totalPatterns) {
      setShowSkeleton(false);
    }
  }, [patternsLoaded, totalPatterns]);

  const handlePatternError = () => {
    if (isImage || !gift?.symbol?.name || !gift?.base_name) {
      setPatternState(AssetFallbackState.FAILED);
      return;
    }

    switch (patternState) {
      case AssetFallbackState.TGS:
        setPatternState(AssetFallbackState.PNG);
        setPatternSrc(
          getGiftAssetUrl(
            'patterns',
            gift.symbol.name,
            AssetFallbackState.PNG,
            gift.base_name,
          ),
        );
        break;
      case AssetFallbackState.PNG:
        setPatternState(AssetFallbackState.FAILED);
        break;
    }
  };

  const handlePatternLoad = useCallback(() => {
    setPatternsLoaded((prev) => {
      const newCount = prev + 1;
      // Hide skeleton when ALL patterns are loaded AND model is loaded
      if (!modelLoading && newCount >= totalPatterns) {
        setShowSkeleton(false);
      }
      return newCount;
    });
  }, [modelLoading, totalPatterns]);

  return {
    modelSrc,
    patternSrc,
    modelState,
    patternState,
    modelLoading,
    showSkeleton,
    patternsLoaded,
    totalPatterns,
    handleModelError,
    handlePatternError,
    handleModelLoad,
    handlePatternLoad,
  };
}

export function TgsOrImageGift({
  isImage,
  gift,
  className = '',
  style = { width: '200px', height: '200px' },
}: TgsOrImageGiftProps) {
  const {
    modelSrc,
    patternSrc,
    modelState,
    patternState,
    modelLoading,
    showSkeleton,
    patternsLoaded,
    totalPatterns,
    handleModelError,
    handlePatternError,
    handleModelLoad,
    handlePatternLoad,
  } = useGiftAssets(gift, isImage);

  const patternArray = generateGiftPatternArray();
  const backgroundStyle = generateGiftBackground(gift);
  const symbolColorFilter = generateGiftColorFilter(gift);

  if (!gift) {
    return (
      <div
        className={`flex items-center justify-center bg-gray-100 rounded-lg ${className}`}
        style={style}
      >
        <div className="text-center p-4">
          <div className="text-gray-500 text-sm">⚠️</div>
          <div className="text-xs text-gray-600">Gift not available</div>
        </div>
      </div>
    );
  }

  // Check if all assets are ready
  const allAssetsReady = !modelLoading && patternsLoaded >= totalPatterns;

  // Show skeleton while assets are loading
  if (showSkeleton && !allAssetsReady) {
    return (
      <div
        className={`relative overflow-hidden rounded-lg ${className}`}
        style={{ ...style, ...backgroundStyle }}
      >
        <TgsSkeleton
          className="w-full h-full"
          style={{ width: '100%', height: '100%' }}
        />
      </div>
    );
  }

  // Render pattern component based on state
  const renderPattern = (pattern: PatternPosition, index: number) => (
    <div
      key={index}
      className="absolute"
      style={{
        top: `${pattern.top}%`,
        left: `${pattern.left}%`,
        width: `${pattern.size.width}%`,
        height: `${pattern.size.height}%`,
        opacity: pattern.opacity,
        filter: symbolColorFilter,
        transform: 'translate(-50%, -50%)',
      }}
    >
      {patternState !== AssetFallbackState.FAILED && patternSrc && (
        <>
          {patternState === AssetFallbackState.TGS ? (
            <TgsViewer
              tgsUrl={patternSrc}
              style={{ width: '100%', height: '100%' }}
              onError={handlePatternError}
              onLoad={handlePatternLoad}
            />
          ) : (
            <Image
              src={patternSrc}
              alt="Pattern"
              fill
              className="object-contain"
              onError={handlePatternError}
              onLoad={handlePatternLoad}
              priority={true}
              loading="eager"
              sizes="(max-width: 768px) 100vw, 50vw"
            />
          )}
        </>
      )}
    </div>
  );

  // Render model component based on state
  const renderModel = () => (
    <>
      {modelState === AssetFallbackState.TGS && modelSrc ? (
        <TgsViewer
          tgsUrl={modelSrc}
          style={{ width: '100%', height: '100%' }}
          className="z-10 group-hover:scale-105 transition-transform duration-200"
          onError={handleModelError}
          onLoad={handleModelLoad}
        />
      ) : modelState === AssetFallbackState.PNG && modelSrc ? (
        <Image
          src={modelSrc}
          alt="Gift Model"
          fill
          className="object-contain z-10 group-hover:scale-105 transition-transform duration-200"
          onError={handleModelError}
          onLoad={handleModelLoad}
          priority={true}
          loading="eager"
          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
        />
      ) : null}
    </>
  );

  // Error state component
  const renderErrorState = () => (
    <div className="absolute inset-0 flex items-center justify-center">
      <div className="text-center p-4">
        <div className="text-white text-sm">⚠️</div>
        <div className="text-xs text-white opacity-80">
          Gift assets not available
        </div>
      </div>
    </div>
  );

  const hasError =
    modelState === AssetFallbackState.FAILED ||
    patternState === AssetFallbackState.FAILED;

  return (
    <div
      className={`group relative overflow-hidden rounded-lg ${className}`}
      style={{ ...style, ...backgroundStyle }}
    >
      {/* Pattern Container */}
      <div
        className="absolute inset-0 transition-opacity duration-300"
        style={{ opacity: allAssetsReady ? 1 : 0 }}
      >
        {patternArray.map(renderPattern)}
      </div>

      {/* Model Container */}
      <div
        className="absolute inset-0 flex items-center justify-center transition-opacity duration-300"
        style={{ opacity: allAssetsReady ? 1 : 0 }}
      >
        {modelState !== AssetFallbackState.FAILED && renderModel()}
      </div>

      {/* Error State */}
      {hasError && renderErrorState()}
    </div>
  );
}
