'use client';

import { RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import type {
  AdminOrderStats,
  GetAdminOrdersPaginatedParams,
} from '@/api/admin-api';
import {
  getAdminOrderCountByStatus,
  getAdminOrdersCount,
  getAdminOrdersPaginated,
  getAdminOrderStats,
  getNonAdminOrderCountByStatus,
  getNonAdminOrdersCount,
  getTotalOrdersCount,
} from '@/api/admin-api';
import { getUserById } from '@/api/user-api';
import { Pagination } from '@/components/ui/pagination';
import { useTableSorting } from '@/hooks/use-table-sorting';
import { useToast } from '@/hooks/use-toast';
import type { OrderEntity, UserEntity } from '@/mikerudenko/marketplace-shared';
import { OrderStatus } from '@/mikerudenko/marketplace-shared';
import { useRootContext } from '@/root-context';

import { AdminOrdersHeader } from './admin-orders-management/admin-orders-header';
import { AdminOrdersStats } from './admin-orders-management/admin-orders-stats';
import { AdminOrdersTable } from './admin-orders-management/admin-orders-table';
import { EditAdminOrderModal } from './admin-orders-management/edit-admin-order-modal';
import { useAdminOrdersActions } from './admin-orders-management/use-admin-orders-actions';

export function AdminOrdersManagement() {
  const { toast } = useToast();
  const { collections } = useRootContext();
  const [stats, setStats] = useState<AdminOrderStats | null>(null);
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingStats, setLoadingStats] = useState(true);
  const [userCache, setUserCache] = useState<Record<string, UserEntity>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const [editingOrder, setEditingOrder] = useState<OrderEntity | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [updatingStats, setUpdatingStats] = useState<Set<string>>(new Set());
  const [selectedOrderIds, setSelectedOrderIds] = useState<Set<string>>(
    new Set(),
  );

  const { sortKey, sortDirection, handleSort } = useTableSorting({
    defaultSortKey: 'createdAt',
    defaultSortDirection: 'desc',
  });

  const pageSize = 25;

  const { handleCancelOrder, handleDeleteOrder } = useAdminOrdersActions();

  const loadStats = useCallback(async () => {
    try {
      setLoadingStats(true);
      const statsData = await getAdminOrderStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error loading admin order stats:', error);
      toast({
        title: 'Error',
        description: 'Failed to load order statistics',
        variant: 'destructive',
      });
    } finally {
      setLoadingStats(false);
    }
  }, [toast]);

  const loadOrders = useCallback(
    async (page: number = currentPage) => {
      try {
        setLoading(true);
        const params: GetAdminOrdersPaginatedParams = {
          page,
          pageSize,
          sortBy: sortKey || 'createdAt',
          sortDirection: sortDirection || 'desc',
        };
        const result = await getAdminOrdersPaginated(params);
        setOrders(result.orders);
        setTotalPages(Math.ceil(result.totalItems / pageSize));

        const userIds = new Set<string>();
        result.orders.forEach((order) => {
          if (order.buyerId) userIds.add(order.buyerId);
          if (order.sellerId) userIds.add(order.sellerId);
        });

        const userPromises = Array.from(userIds).map(async (userId) => {
          if (!userCache[userId]) {
            try {
              const user = await getUserById(userId);
              return { userId, user };
            } catch (error) {
              console.error(`Error loading user ${userId}:`, error);
              return { userId, user: null };
            }
          }
          return { userId, user: userCache[userId] };
        });

        const userResults = await Promise.all(userPromises);
        const newUserCache = { ...userCache };
        userResults.forEach(({ userId, user }) => {
          if (user) {
            newUserCache[userId] = user;
          }
        });
        setUserCache(newUserCache);
      } catch (error) {
        console.error('Error loading admin orders:', error);
        toast({
          title: 'Error',
          description: 'Failed to load orders',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    },
    [toast, userCache, currentPage, pageSize, sortKey, sortDirection],
  );

  const refreshData = useCallback(async () => {
    await Promise.all([loadStats(), loadOrders()]);
  }, [loadStats, loadOrders]);

  const handleUpdateStat = useCallback(
    async (statType: string) => {
      if (!stats) return;

      setUpdatingStats((prev) => new Set(prev).add(statType));

      try {
        let newValue: number;

        switch (statType) {
          case 'totalOrders':
            newValue = await getTotalOrdersCount();
            setStats((prev) =>
              prev ? { ...prev, totalOrders: newValue } : null,
            );
            break;
          case 'nonAdminOrders':
            newValue = await getNonAdminOrdersCount();
            // Update the calculated total in the component
            break;
          case 'adminOrders':
            newValue = await getAdminOrdersCount();
            // Update the calculated total in the component
            break;
          case 'nonAdminActive':
            newValue = await getNonAdminOrderCountByStatus(OrderStatus.ACTIVE);
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    nonAdminOrders: {
                      ...prev.nonAdminOrders,
                      active: newValue,
                    },
                  }
                : null,
            );
            break;
          case 'nonAdminPaid':
            newValue = await getNonAdminOrderCountByStatus(OrderStatus.PAID);
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    nonAdminOrders: { ...prev.nonAdminOrders, paid: newValue },
                  }
                : null,
            );
            break;
          case 'nonAdminGiftSent':
            newValue = await getNonAdminOrderCountByStatus(
              OrderStatus.GIFT_SENT_TO_RELAYER,
            );
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    nonAdminOrders: {
                      ...prev.nonAdminOrders,
                      giftSentToRelayer: newValue,
                    },
                  }
                : null,
            );
            break;
          case 'nonAdminCancelled':
            newValue = await getNonAdminOrderCountByStatus(
              OrderStatus.CANCELLED,
            );
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    nonAdminOrders: {
                      ...prev.nonAdminOrders,
                      cancelled: newValue,
                    },
                  }
                : null,
            );
            break;
          case 'nonAdminFulfilled':
            newValue = await getNonAdminOrderCountByStatus(
              OrderStatus.FULFILLED,
            );
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    nonAdminOrders: {
                      ...prev.nonAdminOrders,
                      fulfilled: newValue,
                    },
                  }
                : null,
            );
            break;
          case 'adminActive':
            newValue = await getAdminOrderCountByStatus(OrderStatus.ACTIVE);
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    adminOrders: { ...prev.adminOrders, active: newValue },
                  }
                : null,
            );
            break;
          case 'adminPaid':
            newValue = await getAdminOrderCountByStatus(OrderStatus.PAID);
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    adminOrders: { ...prev.adminOrders, paid: newValue },
                  }
                : null,
            );
            break;
          case 'adminGiftSent':
            newValue = await getAdminOrderCountByStatus(
              OrderStatus.GIFT_SENT_TO_RELAYER,
            );
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    adminOrders: {
                      ...prev.adminOrders,
                      giftSentToRelayer: newValue,
                    },
                  }
                : null,
            );
            break;
          case 'adminCancelled':
            newValue = await getAdminOrderCountByStatus(OrderStatus.CANCELLED);
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    adminOrders: { ...prev.adminOrders, cancelled: newValue },
                  }
                : null,
            );
            break;
          case 'adminFulfilled':
            newValue = await getAdminOrderCountByStatus(OrderStatus.FULFILLED);
            setStats((prev) =>
              prev
                ? {
                    ...prev,
                    adminOrders: { ...prev.adminOrders, fulfilled: newValue },
                  }
                : null,
            );
            break;
          default:
            console.warn(`Unknown stat type: ${statType}`);
            return;
        }

        toast({
          title: 'Success',
          description: `${statType} updated successfully`,
        });
      } catch (error) {
        console.error(`Error updating ${statType}:`, error);
        toast({
          title: 'Error',
          description: `Failed to update ${statType}`,
          variant: 'destructive',
        });
      } finally {
        setUpdatingStats((prev) => {
          const newSet = new Set(prev);
          newSet.delete(statType);
          return newSet;
        });
      }
    },
    [stats, toast],
  );

  const handlePageChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      setSelectedOrderIds(new Set()); // Clear selection when changing pages
      loadOrders(page);
    },
    [loadOrders],
  );

  const wrappedHandleCancelOrder = useCallback(
    async (orderId: string) => {
      try {
        await handleCancelOrder(orderId);
        await refreshData();
      } catch {
        // Error handling is done in the hook
      }
    },
    [handleCancelOrder, refreshData],
  );

  const wrappedHandleDeleteOrder = useCallback(
    async (orderId: string) => {
      try {
        await handleDeleteOrder(orderId);
        await refreshData();
      } catch {
        // Error handling is done in the hook
      }
    },
    [handleDeleteOrder, refreshData],
  );

  const handleEditOrder = (order: OrderEntity) => {
    setEditingOrder(order);
    setIsEditModalOpen(true);
  };

  const handleEditModalClose = () => {
    setIsEditModalOpen(false);
    setEditingOrder(null);
  };

  const handleEditSuccess = async () => {
    await refreshData();
  };

  const handleOrderSelect = (orderId: string, selected: boolean) => {
    setSelectedOrderIds((prev) => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(orderId);
      } else {
        newSet.delete(orderId);
      }
      return newSet;
    });
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedOrderIds(new Set(orders.map((order) => order.id!)));
    } else {
      setSelectedOrderIds(new Set());
    }
  };

  const handleBatchCancel = async (orderIds: string[]) => {
    const promises = orderIds.map((orderId) => handleCancelOrder(orderId));
    await Promise.all(promises);
    setSelectedOrderIds(new Set());
    await refreshData();
  };

  const handleBatchDelete = async (orderIds: string[]) => {
    const promises = orderIds.map((orderId) => handleDeleteOrder(orderId));
    await Promise.all(promises);
    setSelectedOrderIds(new Set());
    await refreshData();
  };

  useEffect(() => {
    refreshData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Reload data when sorting changes
  useEffect(() => {
    if (sortKey && sortDirection) {
      loadOrders(1);
      setCurrentPage(1);
    }
  }, [sortKey, sortDirection, loadOrders]);

  if (loadingStats || loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminOrdersStats
        stats={stats}
        loading={loadingStats}
        onUpdateStat={handleUpdateStat}
        updatingStats={updatingStats}
      />

      <div className="space-y-4">
        <AdminOrdersHeader
          onRefresh={refreshData}
          selectedOrderIds={selectedOrderIds}
          onBatchCancel={handleBatchCancel}
          onBatchDelete={handleBatchDelete}
        />

        <AdminOrdersTable
          orders={orders}
          userCache={userCache}
          collections={collections}
          onCancelOrder={wrappedHandleCancelOrder}
          onDeleteOrder={wrappedHandleDeleteOrder}
          onEditOrder={handleEditOrder}
          selectedOrderIds={selectedOrderIds}
          onOrderSelect={handleOrderSelect}
          onSelectAll={handleSelectAll}
          sortKey={sortKey}
          sortDirection={sortDirection}
          onSort={handleSort}
        />

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          className="mt-4"
        />
      </div>

      <EditAdminOrderModal
        isOpen={isEditModalOpen}
        onClose={handleEditModalClose}
        onSuccess={handleEditSuccess}
        order={editingOrder}
        collections={collections}
      />
    </div>
  );
}
